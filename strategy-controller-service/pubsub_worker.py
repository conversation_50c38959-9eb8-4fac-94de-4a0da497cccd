import os
import json
import base64
import time
import threading
import logging
from google.cloud import pubsub_v1
import firebase_admin
from firebase_admin import firestore
from datetime import datetime
import binascii
import traceback
import requests

# Configure logging
from logging_config import structured_logger

class PubSubWorker:
    """
    Worker that pulls messages from Pub/Sub and processes them.
    """
    
    def __init__(self, k8s_client, db):
        """
        Initialize the PubSub worker.
        
        Args:
            k8s_client: The Kubernetes client for creating pods
            db: Firestore client for updating strategy status
        """
        self.k8s_client = k8s_client
        self.db = db
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
        self.subscription_id = os.getenv('PUBSUB_SUBSCRIPTION', 'strategy-controller-sub')
        self.running = False
        
        # Initialize Firestore client
        self.db = firestore.Client()
        
        self.subscriber = pubsub_v1.SubscriberClient()
        self.publisher = pubsub_v1.PublisherClient()
        
        # Construct paths
        self.subscription_path = self.subscriber.subscription_path(
            self.project_id, self.subscription_id)
        self.topic_path = self.subscriber.topic_path(
            self.project_id, 'strategy-execution')
        
        structured_logger.info(
            "PubSub worker initialization",
            project_id=self.project_id,
            subscription_id=self.subscription_id,
            subscription_path=self.subscription_path,
            topic_path=self.topic_path
        )
            
        # Create topic if it doesn't exist
        try:
            # First try to get the topic
            structured_logger.info("Checking if topic exists", topic_path=self.topic_path)
            self.publisher.get_topic(topic=self.topic_path)
            structured_logger.info("Topic already exists", topic_path=self.topic_path)
        except Exception as e:
            # Only create if the error is "not found"
            if "not found" in str(e).lower():
                try:
                    structured_logger.info("Topic not found, creating it", topic_path=self.topic_path)
                    self.publisher.create_topic(name=self.topic_path)
                    structured_logger.info(
                        "Created new topic",
                        topic_path=self.topic_path
                    )
                except Exception as create_error:
                    # If topic already exists, that's fine - just continue
                    if "already exists" not in str(create_error).lower():
                        structured_logger.error(
                            "Failed to create topic",
                            topic_path=self.topic_path,
                            error=str(create_error)
                        )
                        raise
            else:
                # If it's a different error, log and raise
                structured_logger.error(
                    "Error checking topic existence",
                    topic_path=self.topic_path,
                    error=str(e)
                )
                raise
        
        # Create or get subscription
        try:
            structured_logger.info("Checking if subscription exists", subscription_path=self.subscription_path)
            subscription = self.subscriber.get_subscription(
                request={"subscription": self.subscription_path}
            )
            structured_logger.info("Subscription already exists", 
                subscription_path=subscription.name,
                topic=subscription.topic
            )
        except Exception as e:
            structured_logger.info("Subscription not found, creating it", 
                subscription_path=self.subscription_path,
                error=str(e)
            )
            try:
                subscription = self.subscriber.create_subscription(
                    request={
                        "name": self.subscription_path,
                        "topic": self.topic_path
                    }
                )
                structured_logger.info(
                    "Created new subscription",
                    subscription_path=subscription.name,
                    topic=subscription.topic
                )
            except Exception as create_error:
                # If subscription already exists, that's fine - just continue
                if "already exists" not in str(create_error).lower():
                    structured_logger.error(
                        "Failed to create subscription",
                        subscription_path=self.subscription_path,
                        topic_path=self.topic_path,
                        error=str(create_error)
                    )
                    raise
        
        # Log initialization
        structured_logger.info(
            "PubSub worker initialized", 
            project_id=self.project_id, 
            subscription=self.subscription_id
        )
    
    def start(self):
        """Start the worker in a background thread."""
        if self.running:
            structured_logger.warning("PubSub worker is already running")
            return
        
        self.running = True
        self.worker_thread = threading.Thread(target=self.run)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        structured_logger.info("PubSub worker started")
    
    def stop(self):
        """Stop the worker."""
        self.running = False
        structured_logger.info("PubSub worker stopped")
    
    def run(self):
        """Main loop for pulling and processing messages."""
        structured_logger.info("PubSub worker running")
        count = 0
        
        while self.running:
            try:
                # Pull up to 5 messages
                response = self.subscriber.pull(
                    request={"subscription": self.subscription_path, "max_messages": 5}
                )
                
                if not response.received_messages:
                    # No messages, wait before trying again
                    if count % 10 == 0:  # Only log every 10th check
                        structured_logger.debug("No messages received, waiting")
                    time.sleep(5)
                    count += 1
                    continue
                
                structured_logger.info(
                    "Processing messages", 
                    count=len(response.received_messages)
                )
                
                for received_message in response.received_messages:
                    try:
                        # Process the message
                        self.process_message(received_message.message)
                        
                        # Acknowledge the message
                        self.subscriber.acknowledge(
                            request={
                                "subscription": self.subscription_path,
                                "ack_ids": [received_message.ack_id]
                            }
                        )
                    except Exception as e:
                        structured_logger.error(
                            "Error processing message", 
                            message_id=received_message.message.message_id,
                            error=str(e),
                            exc_info=True
                        )
                        # Don't acknowledge - will be retried
            
            except Exception as e:
                structured_logger.error(
                    "Error in PubSub worker", 
                    error=str(e),
                    exc_info=True
                )
                # Sleep before trying again to avoid tight loop in case of persistent errors
                time.sleep(10)
    
    def create_strategy_from_message(self, message_data, user_id, strategy_id):
        """
        Create a strategy from a message or fetch it from Firestore.
        This method handles multiple approaches to getting the strategy data.
        
        Args:
            message_data: The parsed message data
            user_id: The user ID
            strategy_id: The strategy ID
            
        Returns:
            The strategy data or None if it couldn't be fetched
        """
        strategy = None
        
        # Approach 1: Try to get strategy from message data
        if "strategy" in message_data:
            strategy = message_data["strategy"]
            structured_logger.info(
                "Found strategy in message data",
                keys=list(strategy.keys()) if isinstance(strategy, dict) else "not a dict"
            )
            return strategy
        
        # Approach 2: Try to get from Firestore directly
        try:
            structured_logger.info("Trying to get strategy from Firestore")
            strategy_doc = self.db.collection("users").document(user_id) \
                .collection("submittedStrategies").document(strategy_id).get()
            
            if strategy_doc.exists:
                strategy_data = strategy_doc.to_dict()
                strategy_json = strategy_data.get("strategy_json")
                
                if strategy_json:
                    try:
                        strategy = json.loads(strategy_json)
                        structured_logger.info(
                            "Successfully loaded strategy from Firestore",
                            keys=list(strategy.keys()) if isinstance(strategy, dict) else "not a dict"
                        )
                        return strategy
                    except json.JSONDecodeError:
                        structured_logger.error("Error parsing strategy JSON from Firestore")
                else:
                    structured_logger.error("No strategy_json field in Firestore document")
            else:
                structured_logger.error("Strategy document not found in Firestore")
        except Exception as e:
            structured_logger.error(
                "Error getting strategy from Firestore",
                error=str(e)
            )
        
        # Approach 3: Try to get via Firebase Functions API
        try:
            structured_logger.info("Trying to get strategy via Firebase Functions API")
            import requests
            
            # Determine if we're in emulator mode
            emulator_host = os.environ.get("FUNCTIONS_EMULATOR_HOST", "127.0.0.1:5001")
            base_url = f"http://{emulator_host}/oryntrade/us-central1/check_strategy_exists"
            
            params = {
                "user_id": user_id,
                "strategy_id": strategy_id
            }
            
            response = requests.get(base_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("exists", False):
                    structured_logger.info(
                        "Strategy exists according to Firebase Functions API",
                        keys=data.get("keys", [])
                    )
                    
                    # Now try to retrieve via emulator host passed in from Firebase
                    try:
                        structured_logger.info("Creating fresh Firestore client")
                        from google.cloud import firestore
                        
                        # Try multiple potential emulator hosts
                        firestore_hosts = [
                            data.get("firestore_host", "127.0.0.1:8080"),
                            "127.0.0.1:8080", 
                            "localhost:8080",
                            "127.0.0.1:8082", 
                            "localhost:8082"
                        ]
                        
                        for host in firestore_hosts:
                            try:
                                structured_logger.info(f"Trying Firestore at {host}")
                                os.environ["FIRESTORE_EMULATOR_HOST"] = host
                                
                                fresh_db = firestore.Client(project=self.project_id)
                                fresh_doc = fresh_db.collection("users").document(user_id) \
                                    .collection("submittedStrategies").document(strategy_id).get()
                                
                                if fresh_doc.exists:
                                    fresh_data = fresh_doc.to_dict()
                                    strategy_json = fresh_data.get("strategy_json")
                                    
                                    if strategy_json:
                                        strategy = json.loads(strategy_json)
                                        structured_logger.info(
                                            f"Successfully loaded strategy from Firestore at {host}",
                                            keys=list(strategy.keys()) if isinstance(strategy, dict) else "not a dict"
                                        )
                                        return strategy
                            except Exception as e:
                                structured_logger.error(
                                    f"Error with Firestore at {host}",
                                    error=str(e)
                                )
                    except Exception as e:
                        structured_logger.error(
                            "Error creating fresh Firestore client",
                            error=str(e)
                        )
                else:
                    structured_logger.error(
                        "Strategy doesn't exist according to Firebase Functions API",
                        available_strategies=data.get("available_strategies", [])
                    )
            else:
                structured_logger.error(
                    "Error response from Firebase Functions API",
                    status_code=response.status_code,
                    response=response.text
                )
        except Exception as e:
            structured_logger.error(
                "Error getting strategy via Firebase Functions API",
                error=str(e),
                traceback=traceback.format_exc()
            )
        
        # Return None if all approaches failed
        return None

    def process_message(self, message):
        """
        Process a single Pub/Sub message.
        
        Args:
            message: The Pub/Sub message object
        """
        try:
            # Extract key attributes
            user_id = message.attributes.get('user_id')
            strategy_id = message.attributes.get('strategy_id')
            
            # Log basic message info
            structured_logger.info(
                "Message received",
                message_id=message.message_id,
                user_id=user_id,
                strategy_id=strategy_id
            )
            
            # Decode the message data
            data_b64 = message.data
            
            # Fix padding issues with base64
            missing_padding = len(data_b64) % 4
            if missing_padding:
                data_b64 += b'=' * (4 - missing_padding)
                
            try:
                decoded_str = base64.b64decode(data_b64).decode("utf-8")
                
                # Parse the JSON message
                message_data = json.loads(decoded_str)
                
                # Rest of the message processing
                # ...

            except (binascii.Error, UnicodeDecodeError) as e:
                # Try an alternative approach for malformed base64
                structured_logger.warning(
                    "Base64 decode error, trying alternative approach", 
                    message_id=message.message_id,
                    error=str(e)
                )
                # Try to decode raw string representation
                try:
                    raw_data = data_b64.decode('utf-8', errors='ignore')
                    # Look for JSON patterns in the raw data
                    start_idx = raw_data.find('{')
                    if start_idx >= 0:
                        # Find matching closing brace
                        open_count = 0
                        for i in range(start_idx, len(raw_data)):
                            if raw_data[i] == '{':
                                open_count += 1
                            elif raw_data[i] == '}':
                                open_count -= 1
                                if open_count == 0:
                                    # Found matching closing brace
                                    json_str = raw_data[start_idx:i+1]
                                    message_data = json.loads(json_str)
                                    structured_logger.info(
                                        "Successfully extracted JSON from raw data",
                                        message_id=message.message_id
                                    )
                                    break
                        else:
                            raise ValueError("Could not find complete JSON object in raw data")
                    else:
                        raise ValueError("Could not find JSON object start in raw data")
                except (json.JSONDecodeError, ValueError) as e:
                    structured_logger.error(
                        "Failed to parse message with alternative approach",
                        message_id=message.message_id,
                        error=str(e)
                    )
                    raise
            
            # Log the parsed message data
            structured_logger.info(
                "Parsed message data", 
                message_id=message.message_id,
                keys=list(message_data.keys())
            )
            
            # Handle both message formats
            user_id = None
            strategy_id = None
            
            # First, try to get user_id and strategy_id from message
            if "strategy" in message_data and isinstance(message_data["strategy"], dict):
                user_id = message_data["strategy"].get("user_id")
                strategy_id = message_data["strategy"].get("id")
                structured_logger.info(
                    "Found IDs in strategy object",
                    user_id=user_id,
                    strategy_id=strategy_id
                )
            
            # If not found in strategy object, try direct fields
            if not user_id and "user_id" in message_data:
                user_id = message_data.get("user_id")
                structured_logger.info(
                    "Found user_id at root level",
                    user_id=user_id
                )
            
            if not strategy_id:
                # Try both id and strategy_id
                if "id" in message_data:
                    strategy_id = message_data.get("id")
                elif "strategy_id" in message_data:
                    strategy_id = message_data.get("strategy_id")
                
                structured_logger.info(
                    "Found strategy_id at root level",
                    strategy_id=strategy_id
                )
            
            if not user_id or not strategy_id:
                structured_logger.warning(
                    "Missing user_id or strategy_id in message", 
                    message_id=message.message_id,
                    user_id=user_id,
                    strategy_id=strategy_id,
                    message_data=message_data
                )
                return
            
            structured_logger.info(
                "Processing strategy", 
                message_id=message.message_id,
                user_id=user_id,
                strategy_id=strategy_id
            )
            
            # Get the strategy data using our multi-approach method
            strategy = self.create_strategy_from_message(message_data, user_id, strategy_id)
            
            if not strategy:
                structured_logger.error(
                    "Failed to get strategy data after trying all approaches",
                    user_id=user_id,
                    strategy_id=strategy_id
                )
                return
                
            # Create the strategy pod
            structured_logger.info(
                "Creating strategy pod",
                user_id=user_id,
                strategy_id=strategy_id
            )
            
            # Ensure strategy has user_id and id fields
            if isinstance(strategy, dict):
                # Add required fields if missing
                if "user_id" not in strategy:
                    strategy["user_id"] = user_id
                if "id" not in strategy and "strategy_id" not in strategy:
                    strategy["id"] = strategy_id
            
            # Convert strategy to JSON for the k8s_client
            strategy_json = json.dumps(strategy)
            
            # Create the pod
            result = self.k8s_client.create_strategy_pod(
                strategy_id=strategy_id,
                user_id=user_id,
                strategy_json=strategy_json
            )
            
            structured_logger.info(
                "Strategy pod creation result",
                user_id=user_id,
                strategy_id=strategy_id,
                result=result
            )
            
            if "error" in result:
                # Update Firestore with error
                try:
                    structured_logger.info(
                        "Updating Firestore with error status",
                        user_id=user_id,
                        strategy_id=strategy_id,
                        error=result["error"]
                    )
                    self.db.collection("users").document(user_id) \
                        .collection("submittedStrategies").document(strategy_id).set({
                            "status": "error",
                            "error": result["error"],
                            "error_at": datetime.utcnow()
                        }, merge=True)
                except Exception as e:
                    structured_logger.error(
                        "Failed to update Firestore with error",
                        user_id=user_id,
                        strategy_id=strategy_id,
                        error=str(e)
                    )
                
                structured_logger.error(
                    "Failed to create strategy pod", 
                    user_id=user_id,
                    strategy_id=strategy_id,
                    error=result["error"]
                )
                return
            
            # Update the strategy status in Firestore
            try:
                strat_ref = self.db.collection("users").document(user_id).collection("submittedStrategies").document(strategy_id)
                
                update_data = {"status": "running"}
                
                # If running as a pod, add pod name
                if "local" in result and result["local"]:
                    # Local process mode
                    update_data["process_id"] = result["process_id"]
                    update_data["runMode"] = "local"
                else:
                    # K8s pod mode
                    update_data["pod_name"] = result["pod_name"]
                    update_data["runMode"] = "pod"
                    
                update_data["lastHeartbeat"] = firestore.SERVER_TIMESTAMP
                
                # Update Firestore
                strat_ref.update(update_data)
                structured_logger.info(
                    "Updated strategy status in Firestore",
                    user_id=user_id,
                    strategy_id=strategy_id,
                    status="running"
                )
            except Exception as e:
                structured_logger.error(
                    "Failed to update Firestore with pod info",
                    user_id=user_id,
                    strategy_id=strategy_id,
                    error=str(e)
                )
                raise
            
            structured_logger.info(
                "Strategy pod created successfully", 
                user_id=user_id,
                strategy_id=strategy_id,
                pod_or_process=result.get("pod_name", result.get("process_id", "unknown"))
            )
            
        except json.JSONDecodeError as e:
            structured_logger.error(
                "Error decoding message", 
                message_id=message.message_id,
                error=str(e)
            )
        except Exception as e:
            structured_logger.error(
                "Error processing message", 
                message_id=message.message_id,
                error=str(e),
                exc_info=True
            )
            raise


# Function to create and start the worker
def start_pubsub_worker(k8s_client, db):
    """
    Create and start a PubSub worker.
    
    Args:
        k8s_client: The Kubernetes client
        db: Firestore client
        
    Returns:
        The PubSub worker instance
    """
    worker = PubSubWorker(k8s_client, db)
    worker.start()
    return worker 